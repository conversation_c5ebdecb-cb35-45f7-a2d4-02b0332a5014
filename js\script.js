'use strict';

/**
 * Todo类 - 表示单个待办事项
 */
class Todo {
    constructor(text, id = null) {
        this.id = id || this.generateId();
        this.text = text.trim();
        this.completed = false;
        this.createdAt = new Date();

        // 新增属性
        this.priority = 'medium';           // 优先级：urgent, high, medium, low, lowest
        this.tags = [];                     // 标签数组
        this.dueDate = null;               // 截止时间 Date对象或null
        this.timeSpent = 0;                // 累计工作时间（毫秒）
        this.completedAt = null;           // 完成时间
        this.pomodoroState = 'idle';       // 番茄钟状态：idle, running, paused
        this.pomodoroStartTime = null;     // 番茄钟开始时间
        this.pomodoroDuration = 25 * 60 * 1000; // 番茄钟时长（默认25分钟）
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    toggle() {
        this.completed = !this.completed;
        if (this.completed) {
            this.completedAt = new Date();
        } else {
            this.completedAt = null;
        }
    }

    updateText(newText) {
        this.text = newText.trim();
    }
}

/**
 * TodoApp类 - 管理整个应用状态
 */
class TodoApp {
    constructor() {
        this.todos = [];
        this.currentFilter = 'all';
        this.editingId = null;

        // 新增属性
        this.availableTags = ['工作', '生活', '学习', '购物', '健康']; // 预设标签
        this.currentPriorityFilter = 'all';     // 优先级过滤
        this.currentTagFilter = 'all';          // 标签过滤
        this.currentTimeFilter = 'all';         // 时间过滤
        this.clockInterval = null;              // 时钟更新定时器
        this.pomodoroIntervals = new Map();     // 番茄钟定时器映射
        this.showAdvancedOptions = false;      // 高级选项显示状态

        this.initializeElements();
        this.bindEvents();
        this.loadFromStorage();
        this.startClock();
        this.render();
    }

    initializeElements() {
        // 表单和输入元素
        this.todoForm = document.getElementById('todoForm');
        this.todoInput = document.getElementById('todoInput');
        this.prioritySelect = document.getElementById('prioritySelect');
        this.dueDateInput = document.getElementById('dueDateInput');
        this.toggleAdvancedBtn = document.getElementById('toggleAdvanced');
        this.advancedOptions = document.getElementById('advancedOptions');

        // 标签相关元素
        this.tagSelector = document.getElementById('tagSelector');
        this.selectedTags = document.getElementById('selectedTags');
        this.tagDropdownBtn = document.getElementById('tagDropdownBtn');
        this.tagDropdown = document.getElementById('tagDropdown');
        this.newTagInput = document.getElementById('newTagInput');
        this.addTagBtn = document.getElementById('addTagBtn');
        this.availableTagsContainer = document.getElementById('availableTags');

        // 过滤元素
        this.filterButtons = document.querySelectorAll('.filter-btn');
        this.priorityFilter = document.getElementById('priorityFilter');
        this.tagFilter = document.getElementById('tagFilter');
        this.timeFilter = document.getElementById('timeFilter');

        // 列表和状态元素
        this.todoList = document.getElementById('todoList');
        this.emptyState = document.getElementById('emptyState');

        // 统计和操作元素
        this.allCount = document.getElementById('allCount');
        this.activeCount = document.getElementById('activeCount');
        this.completedCount = document.getElementById('completedCount');
        this.totalStats = document.getElementById('totalStats');
        this.clearCompletedBtn = document.getElementById('clearCompleted');

        // 时间统计元素
        this.currentTime = document.getElementById('currentTime');
        this.todayAdded = document.getElementById('todayAdded');
        this.todayCompleted = document.getElementById('todayCompleted');
        this.todayWorkTime = document.getElementById('todayWorkTime');
        this.completionRate = document.getElementById('completionRate');

        // 模态框元素
        this.confirmModal = document.getElementById('confirmModal');
        this.modalTitle = document.getElementById('modalTitle');
        this.modalMessage = document.getElementById('modalMessage');
        this.modalConfirm = document.getElementById('modalConfirm');
        this.modalCancel = document.getElementById('modalCancel');
    }

    bindEvents() {
        // 表单提交事件
        this.todoForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.addTodo();
        });

        // 过滤按钮事件
        this.filterButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                this.setFilter(btn.dataset.filter);
            });
        });

        // 清除已完成按钮事件
        this.clearCompletedBtn.addEventListener('click', () => {
            this.showConfirmModal(
                '清除已完成项目',
                '确定要删除所有已完成的待办事项吗？此操作无法撤销。',
                () => this.clearCompleted()
            );
        });

        // 模态框事件
        this.modalCancel.addEventListener('click', () => this.hideConfirmModal());
        this.confirmModal.addEventListener('click', (e) => {
            if (e.target === this.confirmModal) {
                this.hideConfirmModal();
            }
        });

        // 高级选项相关事件
        if (this.toggleAdvancedBtn) {
            this.toggleAdvancedBtn.addEventListener('click', () => this.handleToggleAdvanced());
        }

        if (this.addTagBtn) {
            this.addTagBtn.addEventListener('click', () => this.handleAddTag());
        }

        if (this.newTagInput) {
            this.newTagInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.handleAddTag();
                }
            });
        }

        if (this.tagDropdownBtn) {
            this.tagDropdownBtn.addEventListener('click', () => this.handleTagDropdown());
        }

        // 过滤事件
        if (this.priorityFilter) {
            this.priorityFilter.addEventListener('change', (e) => this.handlePriorityFilter(e));
        }

        if (this.tagFilter) {
            this.tagFilter.addEventListener('change', (e) => this.handleTagFilter(e));
        }

        if (this.timeFilter) {
            this.timeFilter.addEventListener('change', (e) => this.handleTimeFilter(e));
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.cancelEdit();
                this.hideConfirmModal();
                if (this.tagDropdown) {
                    this.tagDropdown.classList.remove('active');
                }
            } else {
                this.handleKeyboardShortcuts(e);
            }
        });

        // 点击外部关闭下拉菜单
        document.addEventListener('click', (e) => {
            if (this.tagDropdown && !this.tagSelector.contains(e.target)) {
                this.tagDropdown.classList.remove('active');
            }
        });

        // 请求通知权限
        this.requestNotificationPermission();
    }

    addTodo() {
        const text = this.todoInput.value.trim();
        if (!text) return;

        const todo = new Todo(text);

        // 设置优先级
        if (this.prioritySelect) {
            todo.priority = this.prioritySelect.value;
        }

        // 设置截止时间
        if (this.dueDateInput && this.dueDateInput.value) {
            todo.dueDate = new Date(this.dueDateInput.value);
        }

        // 设置标签
        const selectedTagElements = this.selectedTags.querySelectorAll('.selected-tag');
        todo.tags = Array.from(selectedTagElements).map(el => el.textContent.replace('×', '').trim());

        this.todos.unshift(todo);
        this.todoInput.value = '';
        if (this.dueDateInput) this.dueDateInput.value = '';
        this.clearSelectedTags();
        this.saveToStorage();
        this.render();

        // 聚焦到输入框
        this.todoInput.focus();
    }

    deleteTodo(id) {
        const index = this.todos.findIndex(todo => todo.id === id);
        if (index > -1) {
            const todoElement = document.querySelector(`[data-id="${id}"]`);
            if (todoElement) {
                todoElement.classList.add('removing');
                setTimeout(() => {
                    this.todos.splice(index, 1);
                    this.saveToStorage();
                    this.render();
                }, 300);
            }
        }
    }

    toggleTodo(id) {
        const todo = this.todos.find(todo => todo.id === id);
        if (todo) {
            todo.toggle();
            this.saveToStorage();
            this.render();
        }
    }

    startEdit(id) {
        this.cancelEdit(); // 取消其他编辑
        this.editingId = id;
        this.render();
        
        // 聚焦到编辑输入框
        const editInput = document.querySelector(`[data-id="${id}"] .todo-edit-input`);
        if (editInput) {
            editInput.focus();
            editInput.select();
        }
    }

    saveEdit(id, newText) {
        const todo = this.todos.find(todo => todo.id === id);
        if (todo && newText.trim()) {
            todo.updateText(newText);
            this.editingId = null;
            this.saveToStorage();
            this.render();
        }
    }

    cancelEdit() {
        this.editingId = null;
        this.render();
    }

    setFilter(filter) {
        this.currentFilter = filter;
        
        // 更新按钮状态
        this.filterButtons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.filter === filter);
            btn.setAttribute('aria-selected', btn.dataset.filter === filter);
        });
        
        this.render();
    }

    clearCompleted() {
        this.todos = this.todos.filter(todo => !todo.completed);
        this.saveToStorage();
        this.render();
        this.hideConfirmModal();
    }



    updateCounts() {
        const total = this.todos.length;
        const completed = this.todos.filter(todo => todo.completed).length;
        const active = total - completed;

        this.allCount.textContent = total;
        this.activeCount.textContent = active;
        this.completedCount.textContent = completed;
        this.totalStats.textContent = `总计: ${total} 项`;

        // 显示/隐藏清除按钮
        this.clearCompletedBtn.style.display = completed > 0 ? 'block' : 'none';
    }

    updateTimeStats() {
        const stats = this.calculateTodayStats();

        // 更新基本统计
        if (this.totalStats) {
            this.totalStats.textContent = `总计: ${this.todos.length} 项`;
        }

        // 更新时间统计面板
        const timeStatsPanel = document.querySelector('.time-stats');
        if (timeStatsPanel) {
            timeStatsPanel.innerHTML = `
                <div class="stat-item">
                    <span class="stat-label">今日新增:</span>
                    <span class="stat-value">${stats.todayAdded}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">今日完成:</span>
                    <span class="stat-value">${stats.todayCompleted}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">工作时间:</span>
                    <span class="stat-value">${this.formatWorkTime(stats.todayWorkTime)}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">完成率:</span>
                    <span class="stat-value">${stats.completionRate}%</span>
                </div>
            `;
        }
    }

    createTodoElement(todo) {
        const li = document.createElement('li');
        li.className = `todo-item ${todo.completed ? 'completed' : ''}`;
        li.setAttribute('data-id', todo.id);
        li.setAttribute('role', 'listitem');

        const isEditing = this.editingId === todo.id;

        // 生成标签HTML
        const tagsHtml = todo.tags.map(tag =>
            `<span class="tag" style="background-color: ${this.generateTagColor(tag)}20; border-color: ${this.generateTagColor(tag)}; color: ${this.generateTagColor(tag)}">${this.escapeHtml(tag)}</span>`
        ).join('');

        // 生成时间信息HTML
        const createdTimeHtml = `<span class="todo-created-time">${this.formatRelativeTime(todo.createdAt)}</span>`;

        let dueDateHtml = '';
        if (todo.dueDate) {
            const dueStatus = this.getDueStatus(todo);
            dueDateHtml = `<span class="todo-due-time ${dueStatus}">${this.formatDueDate(todo.dueDate)}</span>`;
        }

        const workTimeHtml = todo.timeSpent > 0 ?
            `<span class="todo-work-time">工作时间: ${this.formatWorkTime(todo.timeSpent)}</span>` : '';

        li.innerHTML = `
            <div class="todo-priority-indicator" data-priority="${todo.priority}"></div>
            <div class="todo-checkbox ${todo.completed ? 'checked' : ''}"
                 role="checkbox"
                 aria-checked="${todo.completed}"
                 tabindex="0"
                 aria-label="标记为${todo.completed ? '未完成' : '已完成'}">
            </div>
            <div class="todo-content">
                <span class="todo-text ${isEditing ? 'editing' : ''}">${this.escapeHtml(todo.text)}</span>
                <input type="text"
                       class="todo-edit-input ${isEditing ? 'active' : ''}"
                       value="${this.escapeHtml(todo.text)}"
                       maxlength="200">
                ${todo.tags.length > 0 ? `<div class="todo-tags">${tagsHtml}</div>` : ''}
                <div class="todo-meta">
                    ${createdTimeHtml}
                    ${dueDateHtml}
                    ${workTimeHtml}
                </div>
            </div>
            <div class="todo-actions">
                ${isEditing ? `
                    <button class="action-btn save-btn" aria-label="保存编辑">✓</button>
                    <button class="action-btn cancel-btn" aria-label="取消编辑">✕</button>
                ` : `
                    <button class="action-btn pomodoro-btn" aria-label="番茄钟" data-id="${todo.id}">🍅</button>
                    <button class="action-btn edit-btn" aria-label="编辑待办事项">✏️</button>
                    <button class="action-btn delete-btn" aria-label="删除待办事项">🗑️</button>
                `}
            </div>

            <!-- 番茄钟面板 -->
            <div class="pomodoro-panel" id="pomodoroPanel-${todo.id}" style="display: none;">
                <div class="pomodoro-display">
                    <span class="pomodoro-time">25:00</span>
                    <span class="pomodoro-status">准备开始</span>
                </div>
                <div class="pomodoro-controls">
                    <button class="pomodoro-control-btn start-btn" data-id="${todo.id}">▶️</button>
                    <button class="pomodoro-control-btn pause-btn" data-id="${todo.id}" style="display: none;">⏸️</button>
                    <button class="pomodoro-control-btn stop-btn" data-id="${todo.id}">⏹️</button>
                    <button class="pomodoro-control-btn reset-btn" data-id="${todo.id}">🔄</button>
                </div>
                <div class="pomodoro-settings">
                    <label>时长: <input type="number" class="duration-input" value="25" min="1" max="60" data-id="${todo.id}"> 分钟</label>
                </div>
            </div>
        `;

        this.bindTodoEvents(li, todo);
        return li;
    }

    bindTodoEvents(element, todo) {
        const checkbox = element.querySelector('.todo-checkbox');
        const editBtn = element.querySelector('.edit-btn');
        const deleteBtn = element.querySelector('.delete-btn');
        const saveBtn = element.querySelector('.save-btn');
        const cancelBtn = element.querySelector('.cancel-btn');
        const editInput = element.querySelector('.todo-edit-input');
        const todoText = element.querySelector('.todo-text');

        // 番茄钟相关元素
        const pomodoroBtn = element.querySelector('.pomodoro-btn');
        const pomodoroPanel = element.querySelector('.pomodoro-panel');
        const startBtn = element.querySelector('.start-btn');
        const pauseBtn = element.querySelector('.pause-btn');
        const stopBtn = element.querySelector('.stop-btn');
        const resetBtn = element.querySelector('.reset-btn');
        const durationInput = element.querySelector('.duration-input');

        // 标签相关元素
        const tags = element.querySelectorAll('.tag');

        // 复选框事件
        checkbox.addEventListener('click', () => this.toggleTodo(todo.id));
        checkbox.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.toggleTodo(todo.id);
            }
        });

        // 编辑相关事件
        if (editBtn) {
            editBtn.addEventListener('click', () => this.startEdit(todo.id));
        }

        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => {
                this.showConfirmModal(
                    '删除待办事项',
                    `确定要删除"${todo.text}"吗？`,
                    () => this.deleteTodo(todo.id)
                );
            });
        }

        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.saveEdit(todo.id, editInput.value);
            });
        }

        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.cancelEdit());
        }

        // 双击编辑
        todoText.addEventListener('dblclick', () => {
            if (!todo.completed) {
                this.startEdit(todo.id);
            }
        });

        // 编辑输入框事件
        if (editInput) {
            editInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    this.saveEdit(todo.id, editInput.value);
                } else if (e.key === 'Escape') {
                    this.cancelEdit();
                }
            });

            editInput.addEventListener('blur', () => {
                // 延迟执行，避免与按钮点击冲突
                setTimeout(() => {
                    if (this.editingId === todo.id) {
                        this.saveEdit(todo.id, editInput.value);
                    }
                }, 100);
            });
        }

        // 番茄钟事件
        if (pomodoroBtn) {
            pomodoroBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                const isVisible = pomodoroPanel.style.display !== 'none';

                // 隐藏所有其他番茄钟面板
                document.querySelectorAll('.pomodoro-panel').forEach(panel => {
                    panel.style.display = 'none';
                });

                // 切换当前面板
                pomodoroPanel.style.display = isVisible ? 'none' : 'block';

                if (!isVisible) {
                    this.updatePomodoroDisplay(todo.id);
                }
            });
        }

        if (startBtn) {
            startBtn.addEventListener('click', () => this.startPomodoro(todo.id));
        }

        if (pauseBtn) {
            pauseBtn.addEventListener('click', () => this.pausePomodoro(todo.id));
        }

        if (stopBtn) {
            stopBtn.addEventListener('click', () => this.stopPomodoro(todo.id));
        }

        if (resetBtn) {
            resetBtn.addEventListener('click', () => this.resetPomodoro(todo.id));
        }

        if (durationInput) {
            durationInput.addEventListener('change', (e) => {
                const minutes = parseInt(e.target.value);
                if (minutes > 0 && minutes <= 60) {
                    todo.pomodoroDuration = minutes * 60 * 1000;
                    this.saveToStorage();
                    this.updatePomodoroDisplay(todo.id);
                }
            });
        }

        // 标签点击事件（用于过滤）
        tags.forEach(tag => {
            tag.addEventListener('click', (e) => {
                e.stopPropagation();
                const tagName = tag.textContent;
                if (this.tagFilter) {
                    this.tagFilter.value = tagName;
                    this.currentTagFilter = tagName;
                    this.render();
                }
            });
        });

        // 点击外部关闭番茄钟面板
        document.addEventListener('click', (e) => {
            if (!element.contains(e.target)) {
                pomodoroPanel.style.display = 'none';
            }
        });
    }

    render() {
        const filteredTodos = this.getFilteredTodos();

        // 清空列表
        this.todoList.innerHTML = '';

        // 显示/隐藏空状态
        if (filteredTodos.length === 0) {
            this.emptyState.classList.remove('hidden');
            this.todoList.style.display = 'none';
        } else {
            this.emptyState.classList.add('hidden');
            this.todoList.style.display = 'flex';

            // 渲染待办事项
            filteredTodos.forEach(todo => {
                const todoElement = this.createTodoElement(todo);
                this.todoList.appendChild(todoElement);
            });
        }

        // 更新统计
        this.updateCounts();
        this.updateTimeStats();
        this.renderAvailableTags();
        this.renderTagFilter();
    }

    showConfirmModal(title, message, onConfirm) {
        this.modalTitle.textContent = title;
        this.modalMessage.textContent = message;
        this.confirmModal.classList.add('active');
        this.confirmModal.setAttribute('aria-hidden', 'false');
        
        // 移除之前的事件监听器
        this.modalConfirm.replaceWith(this.modalConfirm.cloneNode(true));
        this.modalConfirm = document.getElementById('modalConfirm');
        
        // 添加新的事件监听器
        this.modalConfirm.addEventListener('click', () => {
            onConfirm();
        });
        
        // 聚焦到取消按钮
        this.modalCancel.focus();
    }

    hideConfirmModal() {
        this.confirmModal.classList.remove('active');
        this.confirmModal.setAttribute('aria-hidden', 'true');
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    saveToStorage() {
        try {
            localStorage.setItem('todos', JSON.stringify(this.todos));
            localStorage.setItem('availableTags', JSON.stringify(this.availableTags));
        } catch (error) {
            console.error('保存到本地存储失败:', error);
        }
    }

    loadFromStorage() {
        try {
            const stored = localStorage.getItem('todos');
            if (stored) {
                const data = JSON.parse(stored);
                this.todos = data.map(item => {
                    const todo = new Todo(item.text, item.id);
                    todo.completed = item.completed;
                    todo.createdAt = new Date(item.createdAt);

                    // 向后兼容：为旧数据设置默认值
                    todo.priority = item.priority || 'medium';
                    todo.tags = item.tags || [];
                    todo.dueDate = item.dueDate ? new Date(item.dueDate) : null;
                    todo.timeSpent = item.timeSpent || 0;
                    todo.completedAt = item.completedAt ? new Date(item.completedAt) : null;
                    todo.pomodoroState = item.pomodoroState || 'idle';
                    todo.pomodoroStartTime = item.pomodoroStartTime ? new Date(item.pomodoroStartTime) : null;
                    todo.pomodoroDuration = item.pomodoroDuration || 25 * 60 * 1000;

                    return todo;
                });
            }

            // 加载可用标签
            const storedTags = localStorage.getItem('availableTags');
            if (storedTags) {
                this.availableTags = JSON.parse(storedTags);
            }
        } catch (error) {
            console.error('从本地存储加载失败:', error);
            this.todos = [];
        }
    }

    // 时钟相关方法
    startClock() {
        this.updateClock();
        this.clockInterval = setInterval(() => {
            this.updateClock();
        }, 1000);
    }

    stopClock() {
        if (this.clockInterval) {
            clearInterval(this.clockInterval);
            this.clockInterval = null;
        }
    }

    updateClock() {
        if (this.currentTime) {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
            this.currentTime.textContent = timeString;
        }
    }

    // 相对时间格式化
    formatRelativeTime(date) {
        const now = new Date();
        const diff = now - date;
        const minutes = Math.floor(diff / (1000 * 60));
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));

        if (minutes < 1) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        if (days < 7) return `${days}天前`;

        return date.toLocaleDateString('zh-CN');
    }

    // 标签相关方法
    generateTagColor(tagName) {
        const hash = tagName.split('').reduce((a, b) => {
            a = ((a << 5) - a) + b.charCodeAt(0);
            return a & a;
        }, 0);
        const hue = Math.abs(hash) % 360;
        return `hsl(${hue}, 70%, 50%)`;
    }

    addTag(tagName) {
        const trimmedName = tagName.trim();
        if (trimmedName && !this.availableTags.includes(trimmedName)) {
            this.availableTags.push(trimmedName);
            this.saveToStorage();
            this.renderAvailableTags();
            this.renderTagFilter();
        }
    }

    removeTag(tagName) {
        const index = this.availableTags.indexOf(tagName);
        if (index > -1) {
            this.availableTags.splice(index, 1);
            // 从所有待办事项中移除此标签
            this.todos.forEach(todo => {
                const tagIndex = todo.tags.indexOf(tagName);
                if (tagIndex > -1) {
                    todo.tags.splice(tagIndex, 1);
                }
            });
            this.saveToStorage();
            this.renderAvailableTags();
            this.renderTagFilter();
            this.render();
        }
    }

    renderTagDropdown() {
        if (!this.tagDropdown) return;

        this.tagDropdown.innerHTML = '';
        this.availableTags.forEach(tag => {
            const tagElement = document.createElement('div');
            tagElement.className = 'tag-option';
            tagElement.textContent = tag;
            tagElement.style.backgroundColor = this.generateTagColor(tag) + '20';
            tagElement.style.borderColor = this.generateTagColor(tag);
            tagElement.style.color = this.generateTagColor(tag);

            tagElement.addEventListener('click', () => {
                if (this.tagSelector) {
                    this.tagSelector.value = tag;
                }
                this.tagDropdown.classList.remove('active');
            });

            this.tagDropdown.appendChild(tagElement);
        });
    }

    renderTagFilter() {
        if (!this.tagFilter) return;

        // 保存当前选择
        const currentValue = this.tagFilter.value;

        // 清空并重新填充
        this.tagFilter.innerHTML = '<option value="">所有标签</option>';

        this.availableTags.forEach(tag => {
            const option = document.createElement('option');
            option.value = tag;
            option.textContent = tag;
            this.tagFilter.appendChild(option);
        });

        // 恢复选择
        this.tagFilter.value = currentValue;
    }

    clearSelectedTags() {
        if (this.selectedTags) {
            this.selectedTags.innerHTML = '';
        }
    }

    // 过滤相关方法
    getFilteredTodos() {
        let filtered = this.todos;

        // 基础过滤
        switch (this.currentFilter) {
            case 'active':
                filtered = filtered.filter(todo => !todo.completed);
                break;
            case 'completed':
                filtered = filtered.filter(todo => todo.completed);
                break;
        }

        // 优先级过滤
        if (this.currentPriorityFilter !== 'all') {
            filtered = filtered.filter(todo => todo.priority === this.currentPriorityFilter);
        }

        // 标签过滤
        if (this.currentTagFilter !== 'all') {
            filtered = filtered.filter(todo => todo.tags.includes(this.currentTagFilter));
        }

        // 时间过滤
        if (this.currentTimeFilter !== 'all') {
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);
            const threeDaysLater = new Date(today.getTime() + 3 * 24 * 60 * 60 * 1000);

            switch (this.currentTimeFilter) {
                case 'today-due':
                    filtered = filtered.filter(todo =>
                        todo.dueDate && todo.dueDate >= today && todo.dueDate < tomorrow
                    );
                    break;
                case 'upcoming-due':
                    filtered = filtered.filter(todo =>
                        todo.dueDate && todo.dueDate >= tomorrow && todo.dueDate < threeDaysLater
                    );
                    break;
                case 'overdue':
                    filtered = filtered.filter(todo =>
                        todo.dueDate && todo.dueDate < today
                    );
                    break;
                case 'no-due':
                    filtered = filtered.filter(todo => !todo.dueDate);
                    break;
            }
        }

        // 智能排序
        return this.sortTodos(filtered);
    }

    sortTodos(todos) {
        const priorityOrder = { urgent: 0, high: 1, medium: 2, low: 3, lowest: 4 };

        return todos.sort((a, b) => {
            // 首先按优先级排序
            const priorityDiff = priorityOrder[a.priority] - priorityOrder[b.priority];
            if (priorityDiff !== 0) return priorityDiff;

            // 然后按截止时间排序（有截止时间的优先）
            if (a.dueDate && !b.dueDate) return -1;
            if (!a.dueDate && b.dueDate) return 1;
            if (a.dueDate && b.dueDate) {
                const dueDiff = a.dueDate - b.dueDate;
                if (dueDiff !== 0) return dueDiff;
            }

            // 最后按创建时间排序（新的在前）
            return b.createdAt - a.createdAt;
        });
    }

    // 番茄钟相关方法
    startPomodoro(id) {
        const todo = this.todos.find(t => t.id === id);
        if (!todo) return;

        todo.pomodoroState = 'running';
        todo.pomodoroStartTime = new Date();

        const interval = setInterval(() => {
            this.updatePomodoroDisplay(id);

            const elapsed = new Date() - todo.pomodoroStartTime;
            if (elapsed >= todo.pomodoroDuration) {
                this.completePomodoroSession(id);
            }
        }, 1000);

        this.pomodoroIntervals.set(id, interval);
        this.updatePomodoroDisplay(id);
        this.saveToStorage();
    }

    pausePomodoro(id) {
        const todo = this.todos.find(t => t.id === id);
        if (!todo) return;

        if (todo.pomodoroState === 'running') {
            const elapsed = new Date() - todo.pomodoroStartTime;
            todo.timeSpent += elapsed;
            todo.pomodoroState = 'paused';
        } else if (todo.pomodoroState === 'paused') {
            todo.pomodoroState = 'running';
            todo.pomodoroStartTime = new Date();
        }

        if (todo.pomodoroState === 'paused') {
            const interval = this.pomodoroIntervals.get(id);
            if (interval) {
                clearInterval(interval);
                this.pomodoroIntervals.delete(id);
            }
        } else {
            this.startPomodoro(id);
        }

        this.updatePomodoroDisplay(id);
        this.saveToStorage();
    }

    stopPomodoro(id) {
        const todo = this.todos.find(t => t.id === id);
        if (!todo) return;

        if (todo.pomodoroState === 'running') {
            const elapsed = new Date() - todo.pomodoroStartTime;
            todo.timeSpent += elapsed;
        }

        todo.pomodoroState = 'idle';
        todo.pomodoroStartTime = null;

        const interval = this.pomodoroIntervals.get(id);
        if (interval) {
            clearInterval(interval);
            this.pomodoroIntervals.delete(id);
        }

        this.updatePomodoroDisplay(id);
        this.saveToStorage();
    }

    resetPomodoro(id) {
        const todo = this.todos.find(t => t.id === id);
        if (!todo) return;

        const interval = this.pomodoroIntervals.get(id);
        if (interval) {
            clearInterval(interval);
            this.pomodoroIntervals.delete(id);
        }

        todo.pomodoroState = 'idle';
        todo.pomodoroStartTime = null;
        todo.timeSpent = 0;

        this.updatePomodoroDisplay(id);
        this.saveToStorage();
    }

    updatePomodoroDisplay(id) {
        const todo = this.todos.find(t => t.id === id);
        if (!todo) return;

        const panel = document.getElementById(`pomodoroPanel-${id}`);
        if (!panel) return;

        const timeDisplay = panel.querySelector('.pomodoro-time');
        const statusDisplay = panel.querySelector('.pomodoro-status');
        const startBtn = panel.querySelector('.start-btn');
        const pauseBtn = panel.querySelector('.pause-btn');

        let remainingTime = todo.pomodoroDuration;
        let statusText = '准备开始';

        if (todo.pomodoroState === 'running') {
            const elapsed = new Date() - todo.pomodoroStartTime;
            remainingTime = Math.max(0, todo.pomodoroDuration - elapsed);
            statusText = '正在进行';
            startBtn.style.display = 'none';
            pauseBtn.style.display = 'inline-flex';
        } else if (todo.pomodoroState === 'paused') {
            remainingTime = Math.max(0, todo.pomodoroDuration - todo.timeSpent);
            statusText = '已暂停';
            startBtn.style.display = 'inline-flex';
            pauseBtn.style.display = 'none';
        } else {
            startBtn.style.display = 'inline-flex';
            pauseBtn.style.display = 'none';
        }

        const minutes = Math.floor(remainingTime / (1000 * 60));
        const seconds = Math.floor((remainingTime % (1000 * 60)) / 1000);
        timeDisplay.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        statusDisplay.textContent = statusText;
    }

    completePomodoroSession(id) {
        const todo = this.todos.find(t => t.id === id);
        if (!todo) return;

        todo.timeSpent += todo.pomodoroDuration;
        todo.pomodoroState = 'idle';
        todo.pomodoroStartTime = null;

        const interval = this.pomodoroIntervals.get(id);
        if (interval) {
            clearInterval(interval);
            this.pomodoroIntervals.delete(id);
        }

        this.showNotification('番茄钟完成！', `任务"${todo.text}"的番茄钟时间已完成。`);
        this.updatePomodoroDisplay(id);
        this.saveToStorage();
        this.render();
    }

    async requestNotificationPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            await Notification.requestPermission();
        }
    }

    showNotification(title, message) {
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(title, {
                body: message,
                icon: '📝'
            });
        } else {
            // 备选方案：页面内通知
            this.showPageNotification(title, message);
        }
    }

    showPageNotification(title, message) {
        const notification = document.createElement('div');
        notification.className = 'page-notification';
        notification.innerHTML = `
            <strong>${title}</strong>
            <p>${message}</p>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 5000);
    }

    // 统计相关方法
    calculateTodayStats() {
        const today = new Date();
        const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000);

        const todayAdded = this.todos.filter(todo =>
            todo.createdAt >= todayStart && todo.createdAt < todayEnd
        ).length;

        const todayCompleted = this.todos.filter(todo =>
            todo.completedAt && todo.completedAt >= todayStart && todo.completedAt < todayEnd
        ).length;

        const todayWorkTime = this.todos.reduce((total, todo) => {
            return total + todo.timeSpent;
        }, 0);

        const totalTodos = this.todos.length;
        const completedTodos = this.todos.filter(todo => todo.completed).length;
        const completionRate = totalTodos > 0 ? Math.round((completedTodos / totalTodos) * 100) : 0;

        return {
            todayAdded,
            todayCompleted,
            todayWorkTime,
            completionRate
        };
    }

    formatWorkTime(milliseconds) {
        const hours = Math.floor(milliseconds / (1000 * 60 * 60));
        const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));
        return `${hours}h ${minutes}m`;
    }

    getDueStatus(todo) {
        if (!todo.dueDate) return null;

        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);

        if (todo.dueDate < today) {
            return 'overdue';
        } else if (todo.dueDate >= today && todo.dueDate < tomorrow) {
            return 'due-today';
        } else if (todo.dueDate < new Date(today.getTime() + 3 * 24 * 60 * 60 * 1000)) {
            return 'due-soon';
        }
        return 'normal';
    }

    formatDueDate(date) {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);

        if (date < today) {
            const diffDays = Math.ceil((today - date) / (1000 * 60 * 60 * 24));
            return `逾期 ${diffDays} 天`;
        } else if (date >= today && date < tomorrow) {
            return `今天 ${date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}`;
        } else if (date < new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000)) {
            const diffDays = Math.ceil((date - today) / (1000 * 60 * 60 * 1000));
            return `${diffDays} 天后 ${date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}`;
        } else {
            return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        }
    }

    // 事件处理方法
    handleToggleAdvanced() {
        this.showAdvancedOptions = !this.showAdvancedOptions;
        if (this.advancedOptions) {
            this.advancedOptions.style.display = this.showAdvancedOptions ? 'block' : 'none';
        }
        if (this.toggleAdvancedBtn) {
            this.toggleAdvancedBtn.textContent = this.showAdvancedOptions ? '高级选项 ▲' : '高级选项 ▼';
        }
    }

    handleAddTag() {
        const tagName = this.newTagInput.value.trim();
        if (tagName) {
            this.addTag(tagName);
            this.newTagInput.value = '';
        }
    }

    handleTagDropdown() {
        if (this.tagDropdown) {
            this.tagDropdown.classList.toggle('active');
            this.renderTagDropdown();
        }
    }

    handlePriorityFilter(event) {
        this.currentPriorityFilter = event.target.value;
        this.render();
    }

    handleTagFilter(event) {
        this.currentTagFilter = event.target.value;
        this.render();
    }

    handleTimeFilter(event) {
        this.currentTimeFilter = event.target.value;
        this.render();
    }

    handleKeyboardShortcuts(event) {
        if (event.ctrlKey || event.metaKey) {
            switch (event.key) {
                case '1':
                case '2':
                case '3':
                case '4':
                case '5':
                    event.preventDefault();
                    const priorities = ['urgent', 'high', 'medium', 'low', 'lowest'];
                    if (this.prioritySelect) {
                        this.prioritySelect.value = priorities[parseInt(event.key) - 1];
                    }
                    break;
                case 't':
                    event.preventDefault();
                    if (this.newTagInput) {
                        this.newTagInput.focus();
                    }
                    break;
                case 'd':
                    event.preventDefault();
                    if (this.dueDateInput) {
                        this.dueDateInput.focus();
                    }
                    break;
            }
        }
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new TodoApp();
});
