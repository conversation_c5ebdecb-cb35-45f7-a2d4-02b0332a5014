/* CSS重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS变量定义 */
:root {
    --primary-color: #4f46e5;
    --primary-hover: #4338ca;
    --secondary-color: #6b7280;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;

    --bg-color: #ffffff;
    --surface-color: #f9fafb;
    --border-color: #e5e7eb;
    --text-primary: #111827;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;

    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;

    --transition: all 0.2s ease-in-out;
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

    /* 优先级颜色 */
    --priority-urgent: #ef4444;
    --priority-high: #f59e0b;
    --priority-medium: #eab308;
    --priority-low: #10b981;
    --priority-lowest: #6b7280;

    /* 标签相关 */
    --tag-bg: rgba(79, 70, 229, 0.1);
    --tag-border: rgba(79, 70, 229, 0.3);
    --tag-text: var(--primary-color);

    /* 时间相关 */
    --time-text: var(--text-muted);
    --overdue-color: var(--danger-color);
    --due-soon-color: var(--warning-color);

    /* 番茄钟相关 */
    --pomodoro-bg: var(--surface-color);
    --pomodoro-border: var(--border-color);
    --pomodoro-active: var(--success-color);

    /* 动画时长 */
    --animation-fast: 0.15s;
    --animation-normal: 0.3s;
    --animation-slow: 0.5s;
}

/* 深色主题 */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #111827;
        --surface-color: #1f2937;
        --border-color: #374151;
        --text-primary: #f9fafb;
        --text-secondary: #d1d5db;
        --text-muted: #9ca3af;
    }
}

/* 基础样式 */
body {
    font-family: var(--font-family);
    background-color: var(--bg-color);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    transition: var(--transition);
}

.container {
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem 1rem;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    margin-bottom: 2rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.title-section {
    text-align: left;
}

.title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.subtitle {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

/* 时钟显示 */
.clock-section {
    display: flex;
    align-items: center;
}

.current-time {
    font-family: 'Courier New', monospace;
    font-size: 1rem;
    color: var(--text-secondary);
    background-color: var(--surface-color);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    min-width: 180px;
    text-align: center;
}

/* 输入区域样式 */
.input-section {
    margin-bottom: 2rem;
}

.todo-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.input-group {
    display: flex;
    gap: 0.75rem;
    align-items: stretch;
    flex-wrap: wrap;
}

.todo-input {
    flex: 1;
    min-width: 200px;
    padding: 0.875rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 1rem;
    background-color: var(--bg-color);
    color: var(--text-primary);
    transition: var(--transition);
}

.todo-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* 优先级选择器 */
.priority-select {
    padding: 0.875rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--surface-color);
    color: var(--text-primary);
    font-size: 1rem;
    min-width: 120px;
    cursor: pointer;
    transition: var(--transition);
}

.priority-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.add-btn {
    padding: 0.875rem 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 100px;
    justify-content: center;
}

.add-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.add-btn:active {
    transform: translateY(0);
}

.btn-icon {
    font-size: 1.2rem;
    font-weight: bold;
}

/* 高级选项区域 */
.advanced-options {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 1rem;
    margin-top: 0.5rem;
}

.option-group {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
}

.option-group:last-child {
    margin-bottom: 0;
}

.option-group label {
    font-weight: 500;
    color: var(--text-primary);
    min-width: 80px;
}

.due-date-input {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background-color: var(--bg-color);
    color: var(--text-primary);
    font-size: 0.875rem;
}

.toggle-advanced-btn {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: 0.875rem;
    transition: var(--transition);
    align-self: flex-start;
}

.toggle-advanced-btn:hover {
    background-color: var(--surface-color);
    color: var(--text-primary);
}

/* 标签相关样式 */
.tag-selector {
    position: relative;
    display: inline-block;
    min-width: 200px;
}

.selected-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-bottom: 0.5rem;
}

.selected-tag {
    display: inline-flex;
    align-items: center;
    padding: 0.125rem 0.5rem;
    background-color: var(--tag-bg);
    border: 1px solid var(--tag-border);
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--tag-text);
    cursor: pointer;
}

.selected-tag:hover {
    background-color: var(--tag-border);
}

.tag-dropdown-btn {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background-color: var(--bg-color);
    color: var(--text-primary);
    cursor: pointer;
    text-align: left;
}

.tag-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    max-height: 200px;
    overflow-y: auto;
    z-index: 100;
    display: none;
}

.tag-dropdown.active {
    display: block;
}

.tag-option {
    padding: 0.5rem 1rem;
    cursor: pointer;
    transition: var(--transition);
}

.tag-option:hover {
    background-color: var(--border-color);
}

/* 标签管理区域 */
.tag-management {
    margin-top: 1rem;
    padding: 1rem;
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
}

.tag-management h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.tag-input-group {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.new-tag-input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background-color: var(--bg-color);
    color: var(--text-primary);
}

.add-tag-btn {
    padding: 0.5rem 1rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: 0.875rem;
}

.available-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.available-tag {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    background-color: var(--tag-bg);
    border: 1px solid var(--tag-border);
    border-radius: 1rem;
    font-size: 0.75rem;
    color: var(--tag-text);
}

.remove-tag-btn {
    background: none;
    border: none;
    color: var(--danger-color);
    cursor: pointer;
    font-size: 0.75rem;
    padding: 0;
    margin-left: 0.25rem;
}

/* 过滤按钮样式 */
.filter-section {
    margin-bottom: 1.5rem;
}

.filter-section > * + * {
    margin-top: 1rem;
}

.filter-buttons {
    display: flex;
    gap: 0.5rem;
    background-color: var(--surface-color);
    padding: 0.25rem;
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
}

.filter-btn {
    flex: 1;
    padding: 0.75rem 1rem;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.filter-btn.active {
    background-color: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-sm);
}

.filter-btn:not(.active):hover {
    background-color: var(--border-color);
    color: var(--text-primary);
}

.count {
    background-color: rgba(255, 255, 255, 0.2);
    padding: 0.125rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 600;
}

/* 高级过滤选项 */
.advanced-filters {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    align-items: center;
    padding: 1rem;
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
    white-space: nowrap;
}

.filter-select {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background-color: var(--bg-color);
    color: var(--text-primary);
    font-size: 0.875rem;
    cursor: pointer;
    min-width: 120px;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
}

.filter-btn:not(.active) .count {
    background-color: var(--border-color);
    color: var(--text-muted);
}

/* 主内容区域 */
.main-content {
    flex: 1;
    margin-bottom: 2rem;
}

.todo-list {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

/* 待办事项样式 */
.todo-item {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1rem;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    transition: var(--transition);
    animation: slideIn 0.3s ease-out;
    position: relative;
}

.todo-item:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.todo-item.completed {
    opacity: 0.7;
}

.todo-item.completed .todo-text {
    text-decoration: line-through;
    color: var(--text-muted);
}

/* 优先级指示器 */
.todo-priority-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
    margin-top: 0.5rem;
}

.todo-priority-indicator[data-priority="urgent"] { background-color: var(--priority-urgent); }
.todo-priority-indicator[data-priority="high"] { background-color: var(--priority-high); }
.todo-priority-indicator[data-priority="medium"] { background-color: var(--priority-medium); }
.todo-priority-indicator[data-priority="low"] { background-color: var(--priority-low); }
.todo-priority-indicator[data-priority="lowest"] { background-color: var(--priority-lowest); }

/* 复选框样式 */
.todo-checkbox {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    cursor: pointer;
    position: relative;
    transition: var(--transition);
    flex-shrink: 0;
    margin-top: 0.25rem;
}

.todo-checkbox.checked {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.todo-checkbox.checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.875rem;
    font-weight: bold;
}

/* 待办事项内容区域 */
.todo-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* 文本内容 */
.todo-text {
    font-size: 1rem;
    line-height: 1.5;
    word-break: break-word;
    margin: 0;
}

.todo-text.editing {
    display: none;
}

.todo-edit-input {
    flex: 1;
    padding: 0.5rem;
    border: 2px solid var(--primary-color);
    border-radius: var(--radius-sm);
    font-size: 1rem;
    background-color: var(--bg-color);
    color: var(--text-primary);
    display: none;
}

.todo-edit-input.active {
    display: block;
}

/* 标签显示 */
.todo-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
}

.tag {
    display: inline-flex;
    align-items: center;
    padding: 0.125rem 0.5rem;
    background-color: var(--tag-bg);
    border: 1px solid var(--tag-border);
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--tag-text);
    transition: var(--transition);
}

.tag:hover {
    background-color: var(--tag-border);
    cursor: pointer;
}

/* 待办事项元信息 */
.todo-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    font-size: 0.875rem;
    color: var(--time-text);
}

.todo-created-time,
.todo-due-time,
.todo-work-time {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.todo-due-time.overdue {
    color: var(--overdue-color);
    font-weight: 600;
}

.todo-due-time.due-soon {
    color: var(--due-soon-color);
    font-weight: 600;
}

.todo-due-time.due-today {
    color: var(--warning-color);
    font-weight: 600;
}

/* 操作按钮 */
.todo-actions {
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: var(--transition);
}

.todo-item:hover .todo-actions {
    opacity: 1;
}

.action-btn {
    padding: 0.5rem;
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
}

.edit-btn {
    background-color: var(--warning-color);
    color: white;
}

.edit-btn:hover {
    background-color: #d97706;
}

.delete-btn {
    background-color: var(--danger-color);
    color: white;
}

.delete-btn:hover {
    background-color: #dc2626;
}

.save-btn {
    background-color: var(--success-color);
    color: white;
}

.cancel-btn {
    background-color: var(--secondary-color);
    color: white;
}

.pomodoro-btn {
    background-color: var(--warning-color);
    color: white;
    font-size: 1rem;
}

.pomodoro-btn:hover {
    background-color: #d97706;
}

.pomodoro-btn.active {
    background-color: var(--success-color);
}

/* 番茄钟面板 */
.pomodoro-panel {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--pomodoro-bg);
    border: 1px solid var(--pomodoro-border);
    border-radius: var(--radius-lg);
    padding: 1rem;
    box-shadow: var(--shadow-lg);
    z-index: 50;
    min-width: 250px;
    margin-top: 0.5rem;
}

.pomodoro-display {
    text-align: center;
    margin-bottom: 1rem;
}

.pomodoro-time {
    font-size: 2rem;
    font-weight: bold;
    font-family: 'Courier New', monospace;
    color: var(--text-primary);
    display: block;
}

.pomodoro-status {
    display: block;
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

.pomodoro-controls {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.pomodoro-control-btn {
    padding: 0.5rem;
    border: none;
    border-radius: var(--radius-md);
    background-color: var(--primary-color);
    color: white;
    cursor: pointer;
    transition: var(--transition);
    font-size: 1.2rem;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pomodoro-control-btn:hover {
    background-color: var(--primary-hover);
    transform: scale(1.05);
}

.pomodoro-control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.pomodoro-settings {
    border-top: 1px solid var(--border-color);
    padding-top: 1rem;
}

.pomodoro-settings label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.duration-input {
    width: 60px;
    padding: 0.25rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    text-align: center;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-muted);
}

.empty-state.hidden {
    display: none;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

/* 底部样式 */
.footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
    flex-wrap: wrap;
    gap: 1rem;
}

.stats-panel {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 1;
}

.basic-stats {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.time-stats {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
}

.stat-label {
    color: var(--text-secondary);
}

.stat-value {
    color: var(--text-primary);
    font-weight: 600;
}

.clear-completed-btn {
    padding: 0.5rem 1rem;
    background-color: var(--danger-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
}

.clear-completed-btn:hover {
    background-color: #dc2626;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background-color: var(--surface-color);
    padding: 2rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 400px;
    width: 90%;
    text-align: center;
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 1.5rem;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

/* 动画 */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

.todo-item.removing {
    animation: slideOut 0.3s ease-out forwards;
}

/* 响应式设计 */
@media (max-width: 640px) {
    .container {
        padding: 1rem 0.75rem;
    }

    .header-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .title-section {
        text-align: center;
    }

    .title {
        font-size: 2rem;
    }

    .current-time {
        font-size: 0.875rem;
        min-width: auto;
    }

    .input-group {
        flex-direction: column;
        gap: 0.5rem;
    }

    .priority-select {
        min-width: auto;
    }

    .advanced-options {
        padding: 0.75rem;
    }

    .option-group {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .option-group label {
        min-width: auto;
    }

    .tag-selector {
        min-width: auto;
        width: 100%;
    }

    .advanced-filters {
        flex-direction: column;
        gap: 0.75rem;
        padding: 0.75rem;
    }

    .filter-group {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .filter-select {
        min-width: auto;
        width: 100%;
    }

    .filter-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }

    .filter-btn {
        justify-content: space-between;
    }

    .todo-item {
        padding: 0.75rem;
        gap: 0.75rem;
    }

    .todo-meta {
        flex-direction: column;
        gap: 0.5rem;
    }

    .pomodoro-panel {
        position: fixed;
        top: 50%;
        left: 50%;
        right: auto;
        transform: translate(-50%, -50%);
        min-width: 280px;
        max-width: 90vw;
    }

    .time-stats {
        flex-direction: column;
        gap: 0.5rem;
    }

    .footer {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .todo-actions {
        opacity: 1;
    }
}

/* 页面通知样式 */
.page-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: var(--success-color);
    color: white;
    padding: 1rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    max-width: 300px;
    animation: slideInRight 0.3s ease-out;
}

.page-notification strong {
    display: block;
    margin-bottom: 0.25rem;
    font-weight: 600;
}

.page-notification p {
    margin: 0;
    font-size: 0.875rem;
    opacity: 0.9;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
