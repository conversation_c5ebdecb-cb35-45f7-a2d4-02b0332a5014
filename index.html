<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="一个简洁高效的待办事项管理应用">
    <title>Todo List - 待办事项管理</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <!-- 头部标题区域 -->
        <header class="header">
            <div class="header-content">
                <div class="title-section">
                    <h1 class="title">📝 Todo List</h1>
                    <p class="subtitle">管理您的待办事项</p>
                </div>
                <div class="clock-section">
                    <div class="current-time" id="currentTime">2024-07-31 10:30:45</div>
                </div>
            </div>
        </header>

        <!-- 输入表单区域 -->
        <section class="input-section">
            <form class="todo-form" id="todoForm">
                <div class="input-group">
                    <input
                        type="text"
                        id="todoInput"
                        class="todo-input"
                        placeholder="添加新的待办事项..."
                        aria-label="新待办事项输入框"
                        maxlength="200"
                        required
                    >
                    <select id="prioritySelect" class="priority-select" aria-label="选择优先级">
                        <option value="urgent">🔴 紧急</option>
                        <option value="high">🟠 高</option>
                        <option value="medium" selected>🟡 中</option>
                        <option value="low">🟢 低</option>
                        <option value="lowest">⚪ 最低</option>
                    </select>
                    <button
                        type="submit"
                        class="add-btn"
                        aria-label="添加待办事项"
                    >
                        <span class="btn-text">添加</span>
                        <span class="btn-icon">+</span>
                    </button>
                </div>

                <!-- 高级选项区域 -->
                <div class="advanced-options" id="advancedOptions" style="display: none;">
                    <div class="option-group">
                        <label for="dueDateInput">截止时间：</label>
                        <input type="datetime-local" id="dueDateInput" class="due-date-input">
                    </div>

                    <div class="option-group">
                        <label for="tagSelector">标签：</label>
                        <div class="tag-selector" id="tagSelector">
                            <div class="selected-tags" id="selectedTags"></div>
                            <button type="button" class="tag-dropdown-btn" id="tagDropdownBtn">选择标签 ▼</button>
                            <div class="tag-dropdown" id="tagDropdown">
                                <!-- 标签选项将动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>

                <button type="button" class="toggle-advanced-btn" id="toggleAdvanced">
                    高级选项 ▼
                </button>
            </form>

            <!-- 标签管理区域 -->
            <div class="tag-management" id="tagManagement" style="display: none;">
                <h3>标签管理</h3>
                <div class="tag-input-group">
                    <input type="text" id="newTagInput" class="new-tag-input" placeholder="新标签名称...">
                    <button type="button" id="addTagBtn" class="add-tag-btn">添加标签</button>
                </div>
                <div class="available-tags" id="availableTags">
                    <!-- 可用标签将动态生成 -->
                </div>
            </div>
        </section>

        <!-- 过滤按钮组 -->
        <section class="filter-section">
            <div class="filter-buttons" role="tablist" aria-label="待办事项过滤选项">
                <button
                    class="filter-btn active"
                    data-filter="all"
                    role="tab"
                    aria-selected="true"
                    aria-controls="todoList"
                >
                    全部 <span class="count" id="allCount">0</span>
                </button>
                <button
                    class="filter-btn"
                    data-filter="active"
                    role="tab"
                    aria-selected="false"
                    aria-controls="todoList"
                >
                    未完成 <span class="count" id="activeCount">0</span>
                </button>
                <button
                    class="filter-btn"
                    data-filter="completed"
                    role="tab"
                    aria-selected="false"
                    aria-controls="todoList"
                >
                    已完成 <span class="count" id="completedCount">0</span>
                </button>
            </div>

            <!-- 高级过滤选项 -->
            <div class="advanced-filters">
                <div class="filter-group">
                    <label for="priorityFilter">优先级：</label>
                    <select id="priorityFilter" class="filter-select">
                        <option value="all">全部优先级</option>
                        <option value="urgent">紧急</option>
                        <option value="high">高</option>
                        <option value="medium">中</option>
                        <option value="low">低</option>
                        <option value="lowest">最低</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label for="tagFilter">标签：</label>
                    <select id="tagFilter" class="filter-select">
                        <option value="all">全部标签</option>
                        <!-- 标签选项将动态生成 -->
                    </select>
                </div>

                <div class="filter-group">
                    <label for="timeFilter">时间：</label>
                    <select id="timeFilter" class="filter-select">
                        <option value="all">全部</option>
                        <option value="today-due">今日到期</option>
                        <option value="upcoming-due">即将到期</option>
                        <option value="overdue">已逾期</option>
                        <option value="no-due">无截止时间</option>
                    </select>
                </div>
            </div>
        </section>

        <!-- 待办事项列表 -->
        <main class="main-content">
            <ul class="todo-list" id="todoList" role="list" aria-live="polite">
                <!-- 待办事项将动态插入这里 -->
            </ul>
            
            <!-- 空状态提示 -->
            <div class="empty-state" id="emptyState">
                <div class="empty-icon">📋</div>
                <h3>暂无待办事项</h3>
                <p>添加您的第一个待办事项开始管理任务吧！</p>
            </div>
        </main>

        <!-- 底部操作区域 -->
        <footer class="footer">
            <div class="stats-panel">
                <div class="basic-stats">
                    <span id="totalStats">总计: 0 项</span>
                </div>
                <div class="time-stats" id="timeStats">
                    <div class="stat-item">
                        <span class="stat-label">今日新增:</span>
                        <span class="stat-value" id="todayAdded">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">今日完成:</span>
                        <span class="stat-value" id="todayCompleted">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">今日工作:</span>
                        <span class="stat-value" id="todayWorkTime">0h 0m</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">完成率:</span>
                        <span class="stat-value" id="completionRate">0%</span>
                    </div>
                </div>
            </div>
            <button class="clear-completed-btn" id="clearCompleted" style="display: none;">
                清除已完成
            </button>
        </footer>
    </div>

    <!-- 确认对话框模板 -->
    <div class="modal" id="confirmModal" role="dialog" aria-labelledby="modalTitle" aria-hidden="true">
        <div class="modal-content">
            <h3 id="modalTitle">确认操作</h3>
            <p id="modalMessage">您确定要执行此操作吗？</p>
            <div class="modal-actions">
                <button class="btn btn-secondary" id="modalCancel">取消</button>
                <button class="btn btn-danger" id="modalConfirm">确认</button>
            </div>
        </div>
    </div>

    <script src="js/script.js"></script>
</body>
</html>
